-- Step-by-step script to add menu items for public_header section
-- This script matches the frontend navigationLinks exactly
-- Execute each section separately to handle dependencies

-- STEP 1: Ensure the public_header section exists
INSERT INTO menu_section (name, is_dashboard) 
VALUES ('public_header', false)
ON CONFLICT (name) DO NOTHING;

-- STEP 2: Clear existing menu items for this section (optional - uncomment if needed)
-- DELETE FROM menu_item WHERE section_id = (SELECT id FROM menu_section WHERE name = 'public_header');

-- STEP 3: Insert top-level menu items first
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES 
-- About Us
((SELECT id FROM menu_section WHERE name = 'public_header'), 
 NULL, 'About Us', 'About Us page', 'about, company', 'Learn more about our company',
 '/about-us', 1, '', NULL, NULL, true, false, false, false, NULL, NULL, 1),

-- Products & Services (Parent)
((SELECT id FROM menu_section WHERE name = 'public_header'), 
 NULL, 'Products & Services', 'Our products and services', 'products, services', 'Explore our products and services',
 '#services', 2, '', NULL, NULL, true, true, false, false, NULL, NULL, 1),

-- Optimum Furniture
((SELECT id FROM menu_section WHERE name = 'public_header'), 
 NULL, 'Optimum Furniture', 'Optimum furniture solutions', 'optimum, furniture', 'Premium furniture solutions',
 '#optimum-furniture', 3, '', NULL, NULL, true, false, false, false, NULL, NULL, 1),

-- Rental Furnishings (Parent)
((SELECT id FROM menu_section WHERE name = 'public_header'), 
 NULL, 'Rental Furnishings', 'Rental furnishing services', 'rental, furnishings', 'Complete rental furnishing solutions',
 '#rental-furnishings', 4, '', NULL, NULL, true, true, false, false, NULL, NULL, 1),

-- Our Work
((SELECT id FROM menu_section WHERE name = 'public_header'), 
 NULL, 'Our Work', 'Portfolio of our work', 'portfolio, work, projects', 'View our portfolio and completed projects',
 '#work', 5, '', NULL, NULL, true, false, false, false, NULL, NULL, 1),

-- Contact Us
((SELECT id FROM menu_section WHERE name = 'public_header'), 
 NULL, 'Contact Us', 'Contact information', 'contact, information', 'Get in touch with us',
 '#contact', 6, '', NULL, NULL, true, false, false, false, NULL, NULL, 1);

-- STEP 4: Insert children for "Products & Services"
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES 
-- Products & Services children
((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Products & Services' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Trade Show Displays', 'Trade show display solutions', 'trade show, displays', 'Professional trade show displays', 
 '#services', 1, '', NULL, NULL, true, false, false, false, NULL, NULL, 2),

((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Products & Services' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Trade Show Signage', 'Trade show signage solutions', 'trade show, signage', 'Custom trade show signage', 
 '#services', 2, '', NULL, NULL, true, false, false, false, NULL, NULL, 2),

((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Products & Services' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Trade Show Installation', 'Trade show installation services', 'trade show, installation', 'Professional installation services', 
 '#services', 3, '', NULL, NULL, true, false, false, false, NULL, NULL, 2),

((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Products & Services' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Rental Furnishings', 'Rental furnishing solutions', 'rental, furnishings', 'Quality rental furnishings', 
 '#services', 4, '', NULL, NULL, true, false, false, false, NULL, NULL, 2);

-- STEP 5: Insert children for "Rental Furnishings"
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES 
-- Rental Furnishings children
((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Rental Furnishings' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Seating', 'Rental seating options', 'seating, rental', 'Comfortable rental seating', 
 '#services', 1, '', NULL, NULL, true, false, false, false, NULL, NULL, 2),

((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Rental Furnishings' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Tables & Coverings', 'Tables and covering rentals', 'tables, coverings, rental', 'Quality tables and coverings', 
 '#services', 2, '', NULL, NULL, true, false, false, false, NULL, NULL, 2),

((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Rental Furnishings' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Drape Partitioning', 'Drape partitioning solutions', 'drape, partitioning, rental', 'Professional drape partitioning', 
 '#services', 3, '', NULL, NULL, true, false, false, false, NULL, NULL, 2),

((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Rental Furnishings' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Accessories', 'Rental accessories', 'accessories, rental', 'Complete rental accessories', 
 '#services', 4, '', NULL, NULL, true, false, false, false, NULL, NULL, 2),

((SELECT id FROM menu_section WHERE name = 'public_header'), 
 (SELECT menu_item_id FROM menu_item WHERE name = 'Rental Furnishings' AND section_id = (SELECT id FROM menu_section WHERE name = 'public_header') AND parent_id IS NULL), 
 'Plants & Flowers', 'Plant and flower rentals', 'plants, flowers, rental', 'Beautiful plants and flowers', 
 '#services', 5, '', NULL, NULL, true, false, false, false, NULL, NULL, 2);

-- STEP 6: Verify the inserted data
SELECT 
    ms.name as section_name,
    mi.name as menu_name,
    mi.url,
    mi.display_order,
    mi.level,
    mi.is_parent,
    parent.name as parent_name
FROM menu_item mi
JOIN menu_section ms ON mi.section_id = ms.id
LEFT JOIN menu_item parent ON mi.parent_id = parent.menu_item_id
WHERE ms.name = 'public_header'
ORDER BY 
    CASE WHEN mi.parent_id IS NULL THEN mi.display_order ELSE parent.display_order END,
    mi.parent_id NULLS FIRST,
    mi.display_order;
