-- <PERSON><PERSON>t to add menu items for public_header section
-- This script matches the frontend navigationLinks exactly

-- First, ensure the public_header section exists
INSERT INTO menu_section (name, is_dashboard) 
VALUES ('public_header', false)
ON CONFLICT (name) DO NOTHING;

-- Get the section ID for public_header
DECLARE @SectionId INT = (SELECT id FROM menu_section WHERE name = 'public_header');

-- Clear existing menu items for this section (optional - remove if you want to keep existing items)
-- DELETE FROM menu_item WHERE section_id = @SectionId;

-- Insert main menu items
-- 1. About Us
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES (
    @SectionId, NULL, 'About Us', 'About Us page', 'about, company', 'Learn more about our company',
    '/about-us', 1, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 1
);

-- 2. Products & Services (Parent)
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES (
    @SectionId, NULL, 'Products & Services', 'Our products and services', 'products, services', 'Explore our products and services',
    '#services', 2, '', NULL, NULL, 1, 1, 0, 0, NULL, NULL, 1
);

-- Get the parent ID for Products & Services
DECLARE @ProductsParentId INT = (SELECT menu_item_id FROM menu_item WHERE name = 'Products & Services' AND section_id = @SectionId);

-- Products & Services children
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES 
    (@SectionId, @ProductsParentId, 'Trade Show Displays', 'Trade show display solutions', 'trade show, displays', 'Professional trade show displays', '#services', 1, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2),
    (@SectionId, @ProductsParentId, 'Trade Show Signage', 'Trade show signage solutions', 'trade show, signage', 'Custom trade show signage', '#services', 2, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2),
    (@SectionId, @ProductsParentId, 'Trade Show Installation', 'Trade show installation services', 'trade show, installation', 'Professional installation services', '#services', 3, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2),
    (@SectionId, @ProductsParentId, 'Rental Furnishings', 'Rental furnishing solutions', 'rental, furnishings', 'Quality rental furnishings', '#services', 4, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2);

-- 3. Optimum Furniture
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES (
    @SectionId, NULL, 'Optimum Furniture', 'Optimum furniture solutions', 'optimum, furniture', 'Premium furniture solutions',
    '#optimum-furniture', 3, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 1
);

-- 4. Rental Furnishings (Parent)
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES (
    @SectionId, NULL, 'Rental Furnishings', 'Rental furnishing services', 'rental, furnishings', 'Complete rental furnishing solutions',
    '#rental-furnishings', 4, '', NULL, NULL, 1, 1, 0, 0, NULL, NULL, 1
);

-- Get the parent ID for Rental Furnishings
DECLARE @RentalParentId INT = (SELECT menu_item_id FROM menu_item WHERE name = 'Rental Furnishings' AND section_id = @SectionId AND parent_id IS NULL);

-- Rental Furnishings children
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES 
    (@SectionId, @RentalParentId, 'Seating', 'Rental seating options', 'seating, rental', 'Comfortable rental seating', '#services', 1, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2),
    (@SectionId, @RentalParentId, 'Tables & Coverings', 'Tables and covering rentals', 'tables, coverings, rental', 'Quality tables and coverings', '#services', 2, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2),
    (@SectionId, @RentalParentId, 'Drape Partitioning', 'Drape partitioning solutions', 'drape, partitioning, rental', 'Professional drape partitioning', '#services', 3, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2),
    (@SectionId, @RentalParentId, 'Accessories', 'Rental accessories', 'accessories, rental', 'Complete rental accessories', '#services', 4, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2),
    (@SectionId, @RentalParentId, 'Plants & Flowers', 'Plant and flower rentals', 'plants, flowers, rental', 'Beautiful plants and flowers', '#services', 5, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 2);

-- 5. Our Work
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES (
    @SectionId, NULL, 'Our Work', 'Portfolio of our work', 'portfolio, work, projects', 'View our portfolio and completed projects',
    '#work', 5, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 1
);

-- 6. Contact Us
INSERT INTO menu_item (
    section_id, parent_id, name, description, meta_keywords, meta_description, 
    url, display_order, permission_key, icon_name, target, is_visible, 
    is_parent, is_dashboard, is_static, direction, image_path, level
) VALUES (
    @SectionId, NULL, 'Contact Us', 'Contact information', 'contact, information', 'Get in touch with us',
    '#contact', 6, '', NULL, NULL, 1, 0, 0, 0, NULL, NULL, 1
);
