using goodkey_common.Models;
using goodkey_common.Context;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IShowRepository
    {
        IEnumerable<Shows> GetAll();
        Shows GetById(int id);
        Shows Add(Shows show);
        Shows Update(Shows show);
        bool Delete(int id);
        bool ToggleArchive(int id);

        // Show Promoters methods
        IEnumerable<ShowsPromoters> GetShowPromoters(int showId);
        IEnumerable<ShowsPromoters> GetShowPromotersByCompany(int companyId);
        ShowsPromoters? GetShowPromoterById(int promoterId);
        ShowsPromoters AddShowPromoter(ShowsPromoters showPromoter);
        ShowsPromoters UpdateShowPromoter(ShowsPromoters showPromoter);
        bool DeleteShowPromoter(int promoterId);

        // Show Promoter Taxes methods
        IEnumerable<ShowsPromotersTaxes> GetShowPromoterTaxes(int promoterId);
        bool SetShowPromoterTaxes(int promoterId, IEnumerable<ShowsPromotersTaxes> taxes);
        bool DeleteShowPromoterTax(int taxId);
    }

    public class ShowRepository : IShowRepository
    {
        private readonly GoodkeyContext _context;

        public ShowRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<Shows> GetAll()
        {
            return _context.Shows
                .Include(s => s.CreatedByNavigation)
                .Include(s => s.LocationNavigation)
                .Include(s => s.Province)
                .ToList();
        }

        public Shows GetById(int id)
        {
            return _context.Shows
                .Include(s => s.CreatedByNavigation)
                .Include(s => s.LocationNavigation)
                .Include(s => s.Province)
                .FirstOrDefault(s => s.Id == id);
        }

        public Shows Add(Shows show)
        {
            _context.Shows.Add(show);
            _context.SaveChanges();
            return show;
        }

        public Shows Update(Shows show)
        {
            var existingShow = _context.Shows.Find(show.Id);
            if (existingShow == null)
                return null;

            _context.Entry(existingShow).CurrentValues.SetValues(show);
            _context.SaveChanges();
            return show;
        }

        public bool Delete(int id)
        {
            var show = _context.Shows.Find(id);
            if (show == null)
                return false;

            _context.Shows.Remove(show);
            _context.SaveChanges();
            return true;
        }

        public bool ToggleArchive(int id)
        {
            var show = _context.Shows.Find(id);
            if (show == null)
                return false;

            show.Archive = !show.Archive;
            _context.SaveChanges();
            return true;
        }

        // Show Promoters implementation
        public IEnumerable<ShowsPromoters> GetShowPromoters(int showId)
        {
            return _context.ShowsPromoters
                .Include(sp => sp.Show)
                .Include(sp => sp.Company)
                .Include(sp => sp.BilledToContact)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.TaxType)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.Province)
                .Where(sp => sp.ShowId == showId)
                .ToList();
        }

        public IEnumerable<ShowsPromoters> GetShowPromotersByCompany(int companyId)
        {
            return _context.ShowsPromoters
                .Include(sp => sp.Show)
                    .ThenInclude(s => s.LocationNavigation)
                .Include(sp => sp.Show)
                    .ThenInclude(s => s.Province)
                .Include(sp => sp.Company)
                .Include(sp => sp.BilledToContact)
                .Where(sp => sp.CompanyId == companyId)
                .ToList();
        }

        public ShowsPromoters? GetShowPromoterById(int promoterId)
        {
            return _context.ShowsPromoters
                .Include(sp => sp.Show)
                .Include(sp => sp.Company)
                .Include(sp => sp.BilledToContact)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.TaxType)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.Province)
                .FirstOrDefault(sp => sp.Id == promoterId);
        }

        public ShowsPromoters AddShowPromoter(ShowsPromoters showPromoter)
        {
            _context.ShowsPromoters.Add(showPromoter);
            _context.SaveChanges();
            return showPromoter;
        }

        public ShowsPromoters UpdateShowPromoter(ShowsPromoters showPromoter)
        {
            _context.ShowsPromoters.Update(showPromoter);
            _context.SaveChanges();
            return showPromoter;
        }

        public bool DeleteShowPromoter(int promoterId)
        {
            var showPromoter = _context.ShowsPromoters.FirstOrDefault(sp => sp.Id == promoterId);
            if (showPromoter == null)
                return false;

            _context.ShowsPromoters.Remove(showPromoter);
            _context.SaveChanges();
            return true;
        }

        // Show Promoter Taxes implementation
        public IEnumerable<ShowsPromotersTaxes> GetShowPromoterTaxes(int promoterId)
        {
            return _context.ShowsPromotersTaxes
                .Include(spt => spt.Tax)
                    .ThenInclude(t => t.TaxType)
                .Include(spt => spt.Tax)
                    .ThenInclude(t => t.Province)
                .Where(spt => spt.ShowsPromoterId == promoterId)
                .ToList();
        }

        public bool SetShowPromoterTaxes(int promoterId, IEnumerable<ShowsPromotersTaxes> taxes)
        {
            // Remove existing taxes for this promoter
            var existingTaxes = _context.ShowsPromotersTaxes
                .Where(spt => spt.ShowsPromoterId == promoterId)
                .ToList();

            _context.ShowsPromotersTaxes.RemoveRange(existingTaxes);

            // Add new taxes
            foreach (var tax in taxes)
            {
                tax.ShowsPromoterId = promoterId;
                _context.ShowsPromotersTaxes.Add(tax);
            }

            _context.SaveChanges();
            return true;
        }

        public bool DeleteShowPromoterTax(int taxId)
        {
            var tax = _context.ShowsPromotersTaxes.FirstOrDefault(spt => spt.Id == taxId);
            if (tax == null)
                return false;

            _context.ShowsPromotersTaxes.Remove(tax);
            _context.SaveChanges();
            return true;
        }
    }
}