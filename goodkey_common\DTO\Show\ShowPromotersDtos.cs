using System;

namespace goodkey_common.DTO.Show
{
    public class ShowPromoterDto
    {
        public int Id { get; set; }
        public int ShowId { get; set; }
        public int CompanyId { get; set; }
        public int BilledToContactId { get; set; }
        public bool? ShowSubcontact { get; set; }
        public bool? FloorPlanRequired { get; set; }
        
        // Navigation properties
        public string CompanyName { get; set; }
        public string BilledToContactName { get; set; }
        public string BilledToContactEmail { get; set; }
        public string ShowName { get; set; }
        public string ShowCode { get; set; }
        
        // Associated taxes
        public List<ShowPromoterTaxDto> Taxes { get; set; } = new List<ShowPromoterTaxDto>();
    }

    public class CreateShowPromoterDto
    {
        public int CompanyId { get; set; }
        public int BilledToContactId { get; set; }
        public bool? ShowSubcontact { get; set; }
        public bool? FloorPlanRequired { get; set; }
    }

    public class UpdateShowPromoterDto
    {
        public int CompanyId { get; set; }
        public int BilledToContactId { get; set; }
        public bool? ShowSubcontact { get; set; }
        public bool? FloorPlanRequired { get; set; }
    }

    public class ShowPromoterTaxDto
    {
        public int Id { get; set; }
        public int ShowsPromoterId { get; set; }
        public int TaxId { get; set; }
        public decimal TaxRate { get; set; }
        
        // Navigation properties
        public string TaxTypeName { get; set; }
        public string TaxTypeAbbreviation { get; set; }
        public string ProvinceName { get; set; }
        public string ProvinceCode { get; set; }
    }

    public class CreateShowPromoterTaxDto
    {
        public int TaxId { get; set; }
        public decimal TaxRate { get; set; }
    }

    public class UpdateShowPromoterTaxDto
    {
        public int TaxId { get; set; }
        public decimal TaxRate { get; set; }
    }

    public class SetShowPromoterTaxesDto
    {
        public List<CreateShowPromoterTaxDto> Taxes { get; set; } = new List<CreateShowPromoterTaxDto>();
    }
}
