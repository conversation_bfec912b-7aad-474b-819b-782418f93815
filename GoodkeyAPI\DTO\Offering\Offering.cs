﻿namespace goodkey_cms.DTO.Offering
{
	public class OfferingDto
	{
		public int Id { get; set; }
		public string? Name { get; set; }
		public string? Code { get; set; }
		public string? SupplierItemNumber { get; set; }
		public string? PublicDescription { get; set; }
		public bool? IsActive { get; set; }
		public bool? IsObsolete { get; set; }
		public IEnumerable<PropertyOptionsDto>? Options { get; set; }
	}

	public class PropertyOptionsDto
	{
		public int Id { get; set; }
		public string? Name { get; set; }
		public string? Code { get; set; }
		public bool? IsActive { get; set; }
		public string? Image { get; set; }
	}


	public class OfferingDetailDto
	{
		public int Id { get; set; }
		public string? Name { get; set; }
		public int? CategoryId { get; set; }
		public int? GroupTypeId { get; set; }
		public string? ImagePath { get; set; }
		public string? Code { get; set; }
		public string? SupplierItemNumber { get; set; }
		public string? PublicDescription { get; set; }
		public string? InternalDescription { get; set; }
		public int? DisplayOrder { get; set; }
		public int? UnitChargedId { get; set; }
		public bool? IsUnitTypeEach { get; set; }
		public bool? IsAddOn { get; set; }
		public bool? IsForSmOnly { get; set; }
		public bool? IsInternalOnly { get; set; }
		public IFormFile? Image { get; set; }
		public bool? IsActive { get; set; }
		public bool? IsObsolete { get; set; }
		public DateTime CreatedAt { get; set; }
		public DateTime UpdatedAt { get; set; }
		public int? CreatedById { get; set; }
		public int? UpdatedById { get; set; }

		// Optional related entity names (optional for display)
		public string? CategoryName { get; set; }
		public string? GroupName { get; set; }
		public string? GroupTypeName { get; set; }
		public List<int>? TaxTypeIds { get; set; }
	}


	public class OfferingCreateDto
	{
		public int? CategoryId { get; set; }
		public int? GroupTypeId { get; set; }
		public string? Name { get; set; }
		public string? SupplierItemNumber { get; set; }
		public int? DisplayOrder { get; set; }
		public int? UnitChargedId { get; set; }
		public bool? IsUnitTypeEach { get; set; }
		public bool? IsAddOn { get; set; }
		public bool? IsForSmOnly { get; set; }
		public bool? IsInternalOnly { get; set; }
		public IFormFile? Image { get; set; }
		public string? ImagePath { get; set; }
		public bool? IsActive { get; set; }
		public bool? IsObsolete { get; set; }
		public int[]? TaxType { get; set; }
	}

	public class Description
	{
		public string? PublicDescription { get; set; }
		public string? InternalDescription { get; set; }
	}

	public class GroupTypeWithGroupDto
	{
		public int? GroupTypeId { get; set; }
		public string? GroupTypeName { get; set; }
		public bool? IsAvailable { get; set; }
		public List<GroupWithCategoriesDto>? Group { get; set; }
	}

	public class GroupWithCategoriesDto
	{
		public int? GroupId { get; set; }
		public string? GroupName { get; set; }
		public bool? IsAvailable { get; set; }
		public string? Code { get; set; }
		public List<CategoryWithOfferingsDto>? Categories { get; set; }
	}

	public class CategoryWithOfferingsDto
	{
		public int? CategoryId { get; set; }
		public string? CategoryName { get; set; }
		public List<OfferingDto>? Offerings { get; set; }
		public string? Code { get; set; }
		public bool? IsAvailable { get; set; }
	}


	//Property
	public class OfferingPropertyDetailDto
	{
		public int Id { get; set; }
		public string? Code { get; set; }
		public string? Property1 { get; set; }
		public string? Property2 { get; set; }
		public string? PropertyOption1 { get; set; }
		public string? PropertyOption2 { get; set; }
		public string? Image { get; set; }
		public bool? IsActive { get; set; }
	}

	public class OPropertyDto
	{
		public int Id { get; set; }
		public List<int>? Options { get; set; }
	}

	public class OfferingPropertyRequest
	{
		public List<OPropertyDto>? Property { get; set; }
	}

	public class OfferingPropertyCreateDto
	{
		public int Id { get; set; }
		public string? Name { get; set; }
		public string? PropertyOption1 { get; set; }
		public string? PropertyOption2 { get; set; }
		public string? Code { get; set; }
		public IFormFile? Image { get; set; }
		public string? ImagePath { get; set; }
		public string? SupplierItemNumber { get; set; }
		public bool? IsForSmOnly { get; set; }
		public bool? IsInternalOnly { get; set; }
		public bool? IsActive { get; set; }
	}


	//Add on products

	public class AddonSelectionDto
	{
		public List<int> SelectedOfferings { get; set; } = new List<int>(); // List of selected offering IDs
		public List<int> SelectedOptions { get; set; } = new List<int>(); // List of selected option IDs
	}

}
