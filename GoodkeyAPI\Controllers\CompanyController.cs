using goodkey_cms.DTO.User;
using goodkey_cms.Infrastructure.Extensions;
using goodkey_cms.Infrastructure.Utils;
using goodkey_cms.Repositories;
using goodkey_common.DTO;
using goodkey_common.DTO.Company;
using goodkey_common.DTO.Contact;
using goodkey_common.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace goodkey_cms.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class CompanyController : Controller
	{
		private readonly ICompanyRepository _repo;
		private readonly IUserRepository _repoUser;
		private readonly ILogger<CompanyController> _logger;

		public CompanyController(ICompanyRepository repo, IUserRepository repoUser, ILogger<CompanyController> logger)
		{
			_repo = repo;
			_repoUser = repoUser;
			_logger = logger;
		}

		[HttpGet]
		public GenericRespond<IEnumerable<Company>> GetAll([FromQuery] string? companyGroupName = null)
		{
			var companies = string.IsNullOrWhiteSpace(companyGroupName)
				? _repo.GetAll()
				: _repo.GetByCompanyGroup(companyGroupName);

			return new GenericRespond<IEnumerable<Company>>
			{
				Data = companies.OrderByDescending(x => x.CompanyId).Select(item => new Company
				{
					Id = item.CompanyId,
					Name = item.CompanyName,
					Phone = item.Phone,
					Email = item.Email,
					Address1 = item.Address1,
					Address2 = item.Address2,
					City = item.City,
					Province = item.Province?.ProvinceName,
					PostalCode = item.PostalCode,
					Country = item.Country?.CountryName,
					WebsiteUrl = item.WebsiteUrl,
					AccountNumber = item.AccountNumber,
					CompanyGroup = item.CompanyGroup?.Name,
					Note = item.Note,
					IsArchived = item.IsArchived
				})
			};
		}

		[HttpGet("[action]/{id:int}")]
		public GenericRespond<CompanyDto> Get(int id)
		{
			var item = _repo.Get(id);
			if (item == null)
			{
				return new GenericRespond<CompanyDto>
				{
					Data = null,
					Message = "Company not found",
					StatusCode = 404
				};
			}

			return new GenericRespond<CompanyDto>
			{
				Data = new CompanyDto
				{
					Id = item.CompanyId,
					Name = item.CompanyName,
					Phone = item.Phone,
					Email = item.Email,
					Address1 = item.Address1,
					Address2 = item.Address2,
					City = item.City,
					ProvinceId = item.ProvinceId,
					PostalCode = item.PostalCode,
					CountryId = item.CountryId,
					WebsiteUrl = item.WebsiteUrl,
					AccountNumber = item.AccountNumber,
					CompanyGroup = item.CompanyGroup?.Name,
					Note = item.Note,
					IsArchived = item.IsArchived
				}
			};
		}

		[HttpPatch("[action]/{id:int}")]
		public GenericRespond<bool> Update(int id, CompanyCreateUpdateDto data)
		{
			var username = Request.HttpContext.GetUsername();
			if (string.IsNullOrEmpty(username))
			{
				return new GenericRespond<bool>
				{
					Data = false,
					Message = "Unauthorized",
					StatusCode = 401
				};
			}

			// Validate required fields if needed
			var success = _repo.Update(
				id,
				data.Name ?? string.Empty,
				data.Phone ?? string.Empty,
				data.Email ?? string.Empty,
				data.Address1 ?? string.Empty,
				data.Address2 ?? string.Empty,
				data.PostalCode ?? string.Empty,
				data.City ?? string.Empty,
				data.ProvinceId ?? 0,
				data.CountryId ?? 0,
				data.WebsiteUrl ?? string.Empty,
				data.AccountNumber ?? string.Empty,
				data.CompanyGroup ?? string.Empty,
				data.Note ?? string.Empty,
				data.IsArchived ?? false,
				username
			);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success
					? "Company updated successfully."
					: "Failed to update company. It may not exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}

		[HttpPost("[action]")]
		public GenericRespond<bool> Add(CompanyCreateUpdateDto data)
		{
			var username = Request.HttpContext.GetUsername();
			if (string.IsNullOrEmpty(username))
			{
				return new GenericRespond<bool>
				{
					Data = false,
					Message = "Unauthorized",
					StatusCode = 401
				};
			}

			var success = _repo.Add(
				data.Name ?? string.Empty,
				data.Phone ?? string.Empty,
				data.Email ?? string.Empty,
				data.Address1 ?? string.Empty,
				data.Address2 ?? string.Empty,
				data.PostalCode ?? string.Empty,
				data.City ?? string.Empty,
				data.ProvinceId ?? 0,
				data.CountryId ?? 0,
				data.WebsiteUrl ?? string.Empty,
				data.AccountNumber ?? string.Empty,
				data.CompanyGroup ?? string.Empty,
				data.Note ?? string.Empty,
				data.IsArchived ?? false,
				username
			);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success
					? "Company added successfully."
					: "Failed to add company. It may already exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}

		// Contact endpoints for companies
		[HttpGet("{companyId}/contacts")]
		public GenericRespond<IEnumerable<Contact>> GetCompanyContacts(int companyId)
		{
			var contacts = _repo.GetContactsByCompanyId(companyId);

			return new GenericRespond<IEnumerable<Contact>>
			{
				Data = contacts.Select(item => new Contact
				{
					Id = item.ContactId,
					Name = $"{item.FirstName} {item.LastName}".Trim(),
					ContactType = item.ContactType?.Name,
					Company = item.Company?.CompanyName,
					FirstName = item.FirstName,
					LastName = item.LastName,
					Email = item.Email,
					Telephone = item.Telephone,
					Ext = item.Ext,
					Cellphone = item.Cellphone,
					IsArchived = item.IsArchived,
					FullName = $"{item.FirstName} {item.LastName}".Trim()
				})
			};
		}

		[HttpGet("{companyId}/contacts/{contactId:int}")]
		public GenericRespond<CompanyContactDto> GetContact(int companyId, int contactId)
		{
			var item = _repo.GetContact(contactId);
			if (item == null)
			{
				return new GenericRespond<CompanyContactDto>
				{
					Data = null,
					Message = "Contact not found",
					StatusCode = 404
				};
			}

			return new GenericRespond<CompanyContactDto>
			{
				Data = new CompanyContactDto
				{
					Id = item.ContactId,
					Name = $"{item.FirstName} {item.LastName}".Trim(),
					ContactTypeId = item.ContactTypeId,
					LocationId = item.LocationId,
					CompanyId = item.CompanyId,
					FirstName = item.FirstName,
					LastName = item.LastName,
					Email = item.Email,
					Telephone = item.Telephone,
					Ext = item.Ext,
					Cellphone = item.Cellphone,
					IsArchived = item.IsArchived,
					Username = item.Authuser?.Username,
					Password = "blue" // Always return the default password
				}
			};
		}

		[HttpPost("{companyId}/contacts/add")]
		public GenericRespond<bool> AddContact(int companyId, [FromBody] CreateCompanyContactDto data)
		{
			try
			{

				if (data == null)
				{
					return new GenericRespond<bool>
					{
						Data = false,
						Message = "DEBUG: Request data is null",
						StatusCode = 400
					};
				}

				string username = HttpContext.GetUsername();


				if (string.IsNullOrEmpty(username))
				{
					return new GenericRespond<bool>
					{
						Data = false,
						Message = "DEBUG: Unauthorized - username is null or empty",
						StatusCode = 401
					};
				}



				var contactId = _repo.AddContact(
					data.ContactTypeId,
					companyId,
					data.FirstName ?? string.Empty,
					data.LastName ?? string.Empty,
					data.Email ?? string.Empty,
					data.Telephone ?? string.Empty,
					data.Ext ?? string.Empty,
					data.Cellphone ?? string.Empty,
					data.IsArchived ?? false,
					username
				);



				if (contactId == null)
				{
					return new GenericRespond<bool>
					{
						Data = false,
						Message = "Failed to add contact. Repository returned null contactId.",
						StatusCode = 400
					};
				}




				var userDto = new CreateUserDto
				{
					FirstName = data.FirstName ?? "Contact",
					LastName = data.LastName ?? "User",
					Email = data.Email ?? string.Empty,
					WorkEmail = data.Email ?? string.Empty,
					VerificationEmail = data.Email ?? string.Empty,
					WorkPhoneNumber = data.Telephone ?? string.Empty,
					MobileNumber = data.Cellphone ?? string.Empty,
					StatusId = 1, // Active status
					RoleId = 10, // Default role for contacts
					SalutationId = 1, // Default salutation
					DepartmentId = 1 // Default department
				};

				var userId = _repoUser.CreateUser(username, userDto); // Use the creator's username, not the new username


				if (userId == null)
				{
					return new GenericRespond<bool>
					{
						Data = false,
						Message = "DEBUG: Failed to create user account. CreateUser returned null.",
						StatusCode = 400
					};
				}

				// Set default password "blue"

				var password = "blue";
				var hashedPassword = HashUtility.HashPassword(password);
				_repoUser.SetPassword(userId.Value, hashedPassword);
				Console.WriteLine($"DEBUG: Password set successfully");


				var contact = _repo.GetContact(contactId.Value);
				if (contact != null)
				{

					contact.Authuserid = userId.Value;
					_repo.UpdateContact(
						contactId.Value, contact.ContactTypeId,
						contact.CompanyId, contact.FirstName, contact.LastName, contact.Email, contact.Telephone,
						contact.Ext, contact.Cellphone, contact.IsArchived ?? false, username
					);

				}



				return new GenericRespond<bool>
				{
					Data = true,
					Message = "Contact and user account created successfully.",
					StatusCode = 200
				};
			}
			catch (Exception ex)
			{

				return new GenericRespond<bool>
				{
					Data = false,
					Message = $"DEBUG: Exception occurred: {ex.Message}",
					StatusCode = 500
				};
			}
		}

		[HttpPatch("{companyId}/contacts/update/{contactId:int}")]
		public GenericRespond<bool> UpdateContact(int companyId, int contactId, [FromBody] UpdateCompanyContactDto data)
		{
			string username = HttpContext.GetUsername();

			var success = _repo.UpdateContact(
				contactId,
				data.ContactTypeId,
				companyId,
				data.FirstName ?? string.Empty,
				data.LastName ?? string.Empty,
				data.Email ?? string.Empty,
				data.Telephone ?? string.Empty,
				data.Ext ?? string.Empty,
				data.Cellphone ?? string.Empty,
				data.IsArchived ?? false,
				username
			);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success ? "Contact updated successfully." : "Failed to update contact. It may not exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}

		[HttpDelete("{companyId}/contacts/delete/{contactId:int}")]
		public GenericRespond<bool> DeleteContact(int companyId, int contactId)
		{
			string username = HttpContext.GetUsername();

			var success = _repo.DeleteContact(contactId, username);

			return new GenericRespond<bool>
			{
				Data = success,
				Message = success ? "Contact archived successfully." : "Failed to archive contact. It may not exist or there was a database error.",
				StatusCode = success ? 200 : 400
			};
		}
	}
}
