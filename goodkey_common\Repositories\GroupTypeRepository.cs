﻿using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
	public interface IGroupTypeRepository
	{
		Task<IEnumerable<GroupType>> GetAllAsync();
		Task<GroupType> GetAll(int groupId);
		Task<GroupType> GetGroupTypeByIdAsync(int groupTypeId);
	}

	public class GroupTypeRepository : IGroupTypeRepository
	{
		private readonly GoodkeyContext _context;

		public GroupTypeRepository(GoodkeyContext context)
		{
			_context = context;
		}

		public async Task<GroupType> GetGroupTypeByIdAsync(int groupTypeId)
		{
			return await _context.GroupType.FirstOrDefaultAsync(gt => gt.Id == groupTypeId);
		}

		public async Task<IEnumerable<GroupType>> GetAllAsync()
		{
			return await _context.GroupType.ToListAsync();
		}

		public async Task<GroupType> GetAll(int groupId)
		{
			return await _context.GroupType
				.Include(x => x.Group)
				.ThenInclude(x => x.Category)
				.ThenInclude(x => x.Offering)
				.ThenInclude(x => x.OfferingProperty)
					.ThenInclude(x => x.PropertyOption1)
				.Include(x => x.Group)
				.ThenInclude(x => x.Category)
				.ThenInclude(x => x.Offering)
				.ThenInclude(x => x.OfferingProperty)
					.ThenInclude(x => x.PropertyOption2)
				.FirstOrDefaultAsync(x => x.Id == groupId);
		}


	}
}
