-- Fix Schedules Table Primary Key Script
-- This script ensures the schedules table has proper auto-incrementing primary key

-- =====================================================
-- 1. Check and Fix Primary Key Configuration
-- =====================================================

-- First, let's check if the table exists and its current structure
DO $$
BEGIN
    -- Check if the schedules table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'schedules') THEN
        -- Check if the id column is properly configured as SERIAL
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'schedules' 
            AND column_name = 'id' 
            AND column_default LIKE 'nextval%'
        ) THEN
            -- If id is not auto-incrementing, we need to fix it
            RAISE NOTICE 'Fixing schedules table primary key configuration...';
            
            -- Create a new sequence for the id column
            CREATE SEQUENCE IF NOT EXISTS schedules_id_seq;
            
            -- Set the sequence to start from the current maximum id + 1
            SELECT setval('schedules_id_seq', COALESCE((SELECT MAX(id) FROM schedules), 0) + 1);
            
            -- Alter the id column to use the sequence
            ALTER TABLE schedules ALTER COLUMN id SET DEFAULT nextval('schedules_id_seq');
            ALTER TABLE schedules ALTER COLUMN id SET NOT NULL;
            
            -- Make sure the sequence is owned by the id column
            ALTER SEQUENCE schedules_id_seq OWNED BY schedules.id;
            
            RAISE NOTICE 'Schedules table primary key fixed successfully.';
        ELSE
            RAISE NOTICE 'Schedules table primary key is already properly configured.';
        END IF;
    ELSE
        RAISE NOTICE 'Schedules table does not exist.';
    END IF;
END $$;

-- =====================================================
-- 2. Verify the Configuration
-- =====================================================

-- Check the current structure
SELECT 
    column_name,
    data_type,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'schedules' 
AND column_name = 'id';

-- =====================================================
-- 3. Reset Sequence if Needed
-- =====================================================

-- If you need to reset the sequence to start from a specific number
-- (uncomment and modify the number as needed)
/*
SELECT setval('schedules_id_seq', 1, false);
*/

-- =====================================================
-- 4. Test Insert (Optional)
-- =====================================================

-- Uncomment to test if the auto-increment is working
/*
INSERT INTO schedules (code, name, description, notes, is_active, created_by_id, updated_by_id, created_at, updated_at)
VALUES ('TEST001', 'Test Schedule', 'Test Description', 'Test Notes', true, 1, 1, NOW(), NOW())
RETURNING id;
*/ 