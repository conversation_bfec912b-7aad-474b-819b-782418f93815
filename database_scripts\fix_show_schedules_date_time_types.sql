-- Fix Show Schedules Date and Time Field Types
-- This script updates the show_schedules table to use proper date/time data types

-- =====================================================
-- 1. Backup Current Data (if any exists)
-- =====================================================

-- Create a backup table with current data
CREATE TABLE show_schedules_backup AS 
SELECT * FROM show_schedules;

-- =====================================================
-- 2. Update Data Types
-- =====================================================

-- First, drop the indexes that reference the columns we're changing
DROP INDEX IF EXISTS idx_show_schedules_date;

-- Update the data types
ALTER TABLE show_schedules 
    ALTER COLUMN show_schedule_date TYPE DATE USING show_schedule_date::DATE,
    ALTER COLUMN time_start TYPE TIME USING time_start::TIME,
    ALTER COLUMN time_end TYPE TIME USING time_end::TIME;

-- =====================================================
-- 3. Recreate Indexes
-- =====================================================

-- Recreate the date index with the new data type
CREATE INDEX idx_show_schedules_date ON show_schedules(show_schedule_date);

-- =====================================================
-- 4. Update Comments
-- =====================================================

COMMENT ON COLUMN show_schedules.show_schedule_date IS 'Date of the show schedule (DATE type)';
COMMENT ON COLUMN show_schedules.time_start IS 'Start time of the schedule (TIME type)';
COMMENT ON COLUMN show_schedules.time_end IS 'End time of the schedule (TIME type)';

-- =====================================================
-- 5. Verify Changes
-- =====================================================

-- Display the updated table structure
SELECT 
    column_name,
    data_type,
    column_default,
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'show_schedules' 
ORDER BY ordinal_position;

-- =====================================================
-- 6. Sample Data with Correct Types
-- =====================================================

-- Uncomment to insert sample data with correct types
/*
INSERT INTO show_schedules (
    show_schedule_date, 
    time_start, 
    time_end, 
    show_id, 
    show_schedule_confirmed, 
    show_schedule_comments, 
    created_by,
    apply_schedule_to_service_form
) VALUES 
('2024-07-01'::DATE, '09:00:00'::TIME, '17:00:00'::TIME, 1, true, 'Main exhibition day', 1, true),
('2024-07-02'::DATE, '10:00:00'::TIME, '18:00:00'::TIME, 2, false, 'Second show setup', 1, false);
*/

-- =====================================================
-- 7. Rollback Script (if needed)
-- =====================================================

/*
-- To rollback these changes, run:
DROP INDEX IF EXISTS idx_show_schedules_date;

ALTER TABLE show_schedules 
    ALTER COLUMN show_schedule_date TYPE VARCHAR(50),
    ALTER COLUMN time_start TYPE VARCHAR(50),
    ALTER COLUMN time_end TYPE VARCHAR(50);

CREATE INDEX idx_show_schedules_date ON show_schedules(show_schedule_date);

-- Restore from backup if needed
-- DROP TABLE show_schedules;
-- ALTER TABLE show_schedules_backup RENAME TO show_schedules;
*/ 