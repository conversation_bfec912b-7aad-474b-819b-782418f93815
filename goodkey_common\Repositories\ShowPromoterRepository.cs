using goodkey_common.Context;
using goodkey_common.Models;
using Microsoft.EntityFrameworkCore;

namespace goodkey_common.Repositories
{
    public interface IShowPromoterRepository
    {
        IEnumerable<ShowsPromoters> GetAll();
        ShowsPromoters? GetById(int id);
        IEnumerable<ShowsPromoters> GetByShowId(int showId);
        ShowsPromoters Add(ShowsPromoters showPromoter);
        ShowsPromoters Update(ShowsPromoters showPromoter);
        bool Delete(int id);
    }

    public class ShowPromoterRepository : IShowPromoterRepository
    {
        private readonly GoodkeyContext _context;

        public ShowPromoterRepository(GoodkeyContext context)
        {
            _context = context;
        }

        public IEnumerable<ShowsPromoters> GetAll()
        {
            return _context.ShowsPromoters
                .Include(sp => sp.Show)
                .Include(sp => sp.Company)
                .Include(sp => sp.BilledToContact)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.TaxType)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.Province)
                .ToList();
        }

        public ShowsPromoters? GetById(int id)
        {
            return _context.ShowsPromoters
                .Include(sp => sp.Show)
                .Include(sp => sp.Company)
                .Include(sp => sp.BilledToContact)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.TaxType)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.Province)
                .FirstOrDefault(sp => sp.Id == id);
        }

        public IEnumerable<ShowsPromoters> GetByShowId(int showId)
        {
            return _context.ShowsPromoters
                .Include(sp => sp.Show)
                .Include(sp => sp.Company)
                .Include(sp => sp.BilledToContact)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.TaxType)
                .Include(sp => sp.ShowsPromotersTaxes)
                    .ThenInclude(spt => spt.Tax)
                        .ThenInclude(t => t.Province)
                .Where(sp => sp.ShowId == showId)
                .ToList();
        }

        public ShowsPromoters Add(ShowsPromoters showPromoter)
        {
            _context.ShowsPromoters.Add(showPromoter);
            _context.SaveChanges();
            return showPromoter;
        }

        public ShowsPromoters Update(ShowsPromoters showPromoter)
        {
            _context.ShowsPromoters.Update(showPromoter);
            _context.SaveChanges();
            return showPromoter;
        }

        public bool Delete(int id)
        {
            var showPromoter = _context.ShowsPromoters.FirstOrDefault(sp => sp.Id == id);
            if (showPromoter == null)
                return false;

            _context.ShowsPromoters.Remove(showPromoter);
            _context.SaveChanges();
            return true;
        }
    }
}
